// src/controllers/phrsPaymentController.ts
import { Request, Response } from 'express';
import { IapProduct, IapPurchase, UserWallet, Booster, ActiveBooster, VipMembership } from '../models';
import { MyRequest } from '../types/customRequest';
import { tFromRequest } from '../i18n';
import { sequelize } from '../config/db';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { Op, QueryTypes } from 'sequelize';
import { TimeWarpService } from '../services/timeWarpService';

/**
 * PHRS支付控制器
 * 处理使用PHRS余额购买IAP道具的逻辑
 */
export class PhrsPaymentController {

  /**
   * 使用PHRS购买IAP道具
   * POST /api/phrs-payment/purchase
   */
  public async purchaseWithPhrs(req: MyRequest, res: Response): Promise<void> {
    const transaction = await sequelize.transaction();
    
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: tFromRequest(req, 'errors.unauthorized')
        });
        return;
      }

      const { productId } = req.body;

      if (!productId) {
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.paramValidation')
        });
        return;
      }

      // 获取用户钱包
      const wallet = await UserWallet.findByPk(walletId, { transaction });
      if (!wallet) {
        await transaction.rollback();
        res.status(404).json({
          ok: false,
          message: tFromRequest(req, 'errors.userWalletNotFound')
        });
        return;
      }

      // 获取商品信息
      const product = await IapProduct.findByPk(productId, { transaction });
      if (!product || !product.isActive) {
        await transaction.rollback();
        res.status(404).json({
          ok: false,
          message: tFromRequest(req, 'errors.iap.productNotFound')
        });
        return;
      }

      // 检查商品是否支持PHRS支付（这里假设所有商品都支持，实际可以添加字段控制）
      if (!product.priceKaia) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.iap.phrsPaymentNotSupported')
        });
        return;
      }

      // 使用KAIA价格作为PHRS价格（1:1比例，可以根据需要调整）
      const phrsPrice = new BigNumber(product.priceKaia.toString());
      const userPhrsBalance = new BigNumber(wallet.phrsBalance?.toString() || '0');

      // 检查余额是否足够
      if (userPhrsBalance.isLessThan(phrsPrice)) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.iap.insufficientPhrsBalance'),
          data: {
            required: phrsPrice.toFixed(18),
            available: userPhrsBalance.toFixed(18)
          }
        });
        return;
      }

      // 检查购买限制
      const purchaseLimitCheck = await this.checkPurchaseLimit(walletId, product, transaction);
      if (!purchaseLimitCheck.allowed) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: purchaseLimitCheck.message
        });
        return;
      }

      // 使用原子操作扣减PHRS余额
      const phrsAmountStr = phrsPrice.toFixed(18);

      const updateResult = await sequelize.query(
        `UPDATE user_wallets
         SET phrsBalance = phrsBalance - :phrsAmount,
             lastPhrsUpdateTime = :updateTime,
             updatedAt = :updateTime
         WHERE id = :walletId AND phrsBalance >= :phrsAmount`,
        {
          replacements: {
            phrsAmount: phrsAmountStr,
            updateTime: new Date(),
            walletId: walletId
          },
          type: QueryTypes.UPDATE,
          transaction
        }
      );

      // MySQL UPDATE 查询返回 [results, metadata]，metadata 包含 affectedRows
      const affectedRows = Array.isArray(updateResult) ? updateResult[1] : 0;

      if (affectedRows === 0) {
        await transaction.rollback();
        res.status(400).json({
          ok: false,
          message: tFromRequest(req, 'errors.iap.insufficientPhrsBalance'),
          data: {
            required: phrsPrice.toFixed(18),
            available: userPhrsBalance.toFixed(18)
          }
        });
        return;
      }

      // 获取更新后的余额
      const updatedWallet = await UserWallet.findByPk(walletId, {
        attributes: ['phrsBalance'],
        transaction
      });
      const newBalance = new BigNumber(updatedWallet?.phrsBalance?.toString() || '0');

      // 创建购买记录
      const purchase = await IapPurchase.create({
        walletId,
        productId: product.id,
        paymentId: `phrs_${Date.now()}_${walletId}`, // 生成唯一的支付ID
        paymentMethod: 'phrs',
        amount: phrsPrice.toNumber(),
        currency: 'PHRS',
        status: 'FINALIZED', // PHRS支付直接完成
        statusChecked: true,
        purchaseDate: dayjs().toDate()
      }, { transaction });

      // 发放商品
      await this.fulfillPurchase(purchase, product, wallet, transaction);

      await transaction.commit();

      res.json({
        ok: true,
        message: tFromRequest(req, 'success.iap.purchaseCompleted'),
        data: {
          purchaseId: purchase.id,
          productName: product.name,
          phrsPaid: phrsPrice.toFixed(18),
          remainingBalance: newBalance.toFixed(18),
          product: {
            id: product.id,
            name: product.name,
            type: product.type
          }
        }
      });

    } catch (error: any) {
      await transaction.rollback();
      console.error('PHRS购买失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 获取用户PHRS余额和购买历史
   * GET /api/phrs-payment/balance
   */
  public async getPhrsBalance(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        res.status(401).json({
          ok: false,
          message: tFromRequest(req, 'errors.unauthorized')
        });
        return;
      }

      const wallet = await UserWallet.findByPk(walletId);
      if (!wallet) {
        res.status(404).json({
          ok: false,
          message: tFromRequest(req, 'errors.userWalletNotFound')
        });
        return;
      }

      // 获取PHRS购买历史
      const phrsePurchases = await IapPurchase.findAll({
        where: {
          walletId,
          paymentMethod: 'phrs',
          status: 'FINALIZED'
        },
        order: [['createdAt', 'DESC']],
        limit: 20
      });

      // 获取相关产品信息
      const productIds = phrsePurchases.map(p => p.productId);
      const products = await IapProduct.findAll({
        where: { id: productIds },
        attributes: ['id', 'name', 'type']
      });
      const productMap = new Map(products.map(p => [p.id, p]));

      res.json({
        ok: true,
        data: {
          phrsBalance: wallet.phrsBalance || '0',
          phrsWalletAddress: wallet.phrsWalletAddress,
          lastPhrsUpdateTime: wallet.lastPhrsUpdateTime,
          recentPurchases: phrsePurchases.map(purchase => {
            const product = productMap.get(purchase.productId);
            return {
              id: purchase.id,
              productName: product?.name,
              productType: product?.type,
              amount: purchase.amount,
              purchaseDate: purchase.purchaseDate,
              createdAt: purchase.createdAt
            };
          })
        }
      });

    } catch (error: any) {
      console.error('获取PHRS余额失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 获取支持PHRS支付的商品列表
   * GET /api/phrs-payment/products
   */
  public async getPhrsProducts(req: MyRequest, res: Response): Promise<void> {
    try {
      const products = await IapProduct.findAll({
        where: {
          isActive: true
        },
        order: [['id', 'ASC']]
      });

      // 过滤出支持PHRS支付的商品
      const phrsProducts = products.filter(product => product.priceKaia != null);

      res.json({
        ok: true,
        data: {
          products: phrsProducts.map(product => ({
            id: product.id,
            productId: product.productId,
            name: product.name,
            type: product.type,
            phrsPrice: product.priceKaia, // 使用KAIA价格作为PHRS价格
            multiplier: product.multiplier,
            duration: product.duration,
            quantity: product.quantity,
            dailyLimit: product.dailyLimit,
            accountLimit: product.accountLimit,
            description: product.description
          }))
        }
      });

    } catch (error: any) {
      console.error('获取PHRS商品列表失败:', error);
      res.status(500).json({
        ok: false,
        message: tFromRequest(req, 'errors.serverError'),
        details: error.message
      });
    }
  }

  /**
   * 检查购买限制
   */
  private async checkPurchaseLimit(
    walletId: number,
    product: IapProduct,
    transaction: any
  ): Promise<{ allowed: boolean; message?: string }> {
    const today = dayjs().startOf('day').toDate();
    const tomorrow = dayjs().endOf('day').toDate();

    // 检查每日限制
    if (product.dailyLimit > 0) {
      const todayPurchases = await IapPurchase.count({
        where: {
          walletId,
          productId: product.id,
          status: 'FINALIZED',
          createdAt: {
            [Op.between]: [today, tomorrow]
          }
        },
        transaction
      });

      if (todayPurchases >= product.dailyLimit) {
        return {
          allowed: false,
          message: 'Daily purchase limit exceeded'
        };
      }
    }

    // 检查账号限制
    if (product.accountLimit && product.accountLimit > 0) {
      const totalPurchases = await IapPurchase.count({
        where: {
          walletId,
          productId: product.id,
          status: 'FINALIZED'
        },
        transaction
      });

      if (totalPurchases >= product.accountLimit) {
        return {
          allowed: false,
          message: 'Account purchase limit exceeded'
        };
      }
    }

    // VIP会员特殊检查
    if (product.type === 'vip_membership') {
      const activeVip = await VipMembership.findOne({
        where: {
          walletId,
          isActive: true,
          endTime: { [Op.gt]: new Date() }
        },
        transaction
      });

      if (activeVip) {
        return {
          allowed: false,
          message: 'VIP membership already active'
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 发放购买的商品
   */
  private async fulfillPurchase(
    purchase: IapPurchase,
    product: IapProduct,
    wallet: UserWallet,
    transaction: any
  ): Promise<void> {
    const walletId = wallet.id;

    switch (product.type) {
      case 'speed_boost':
      case 'time_warp':
        // 添加道具到用户背包
        const existingBooster = await Booster.findOne({
          where: {
            walletId,
            type: product.type,
            multiplier: product.multiplier,
            duration: product.duration
          },
          transaction
        });

        if (existingBooster) {
          existingBooster.quantity += (product.quantity || 1);
          await existingBooster.save({ transaction });
        } else {
          await Booster.create({
            walletId,
            type: product.type,
            multiplier: product.multiplier || 1,
            duration: product.duration || 3600,
            quantity: product.quantity || 1
          }, { transaction });
        }

        // 如果是时间跳跃，立即使用
        if (product.type === 'time_warp') {
          await TimeWarpService.executeTimeWarp(walletId, product.duration || 1, transaction, product.id, purchase.id);
        }
        break;

      case 'vip_membership':
        // 创建VIP会员记录
        const config = product.config as any;
        const durationDays = config?.durationDays || 30;
        
        await VipMembership.create({
          walletId,
          startTime: new Date(),
          endTime: dayjs().add(durationDays, 'day').toDate(),
          isActive: true
        }, { transaction });
        break;

      case 'special_offer':
        // 处理特殊优惠包
        const offerConfig = product.config as any;
        if (offerConfig?.gems) {
          const currentGems = new BigNumber(wallet.gem?.toString() || '0');
          const newGems = currentGems.plus(new BigNumber(offerConfig.gems));
          await wallet.update({ gem: newGems.toFixed(3) }, { transaction });
        }
        break;

      default:
        console.warn(`未知的商品类型: ${product.type}`);
    }
  }
}

export const phrsPaymentController = new PhrsPaymentController();
