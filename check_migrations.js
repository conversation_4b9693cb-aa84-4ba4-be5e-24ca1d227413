const fs = require('fs');
const path = require('path');

const migrationsDir = './migrations';

// 获取所有迁移文件
const migrationFiles = fs.readdirSync(migrationsDir)
  .filter(file => file.endsWith('.js'))
  .sort();

console.log('检查迁移文件中的存在性判断...\n');

const needsFixing = [];

migrationFiles.forEach(file => {
  const filePath = path.join(migrationsDir, file);
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否有表存在性判断
  const hasTableCheck = content.includes('showAllTables()') || 
                       content.includes('tables.includes(') ||
                       content.includes('describeTable(') ||
                       content.includes('showIndex(');
  
  // 检查是否是创建表的迁移
  const isCreateTable = content.includes('createTable(');
  
  // 检查是否是添加列的迁移
  const isAddColumn = content.includes('addColumn(');
  
  // 检查是否是添加索引的迁移
  const isAddIndex = content.includes('addIndex(');
  
  // 检查是否是修改列的迁移
  const isChangeColumn = content.includes('changeColumn(');
  
  // 检查是否是重命名列的迁移
  const isRenameColumn = content.includes('renameColumn(');
  
  if ((isCreateTable || isAddColumn || isAddIndex || isChangeColumn || isRenameColumn) && !hasTableCheck) {
    needsFixing.push({
      file,
      isCreateTable,
      isAddColumn,
      isAddIndex,
      isChangeColumn,
      isRenameColumn
    });
  }
  
  console.log(`${file}: ${hasTableCheck ? '✅ 有判断' : '❌ 无判断'} ${isCreateTable ? '[创建表]' : ''} ${isAddColumn ? '[添加列]' : ''} ${isAddIndex ? '[添加索引]' : ''} ${isChangeColumn ? '[修改列]' : ''} ${isRenameColumn ? '[重命名列]' : ''}`);
});

console.log(`\n需要修复的文件 (${needsFixing.length} 个):`);
needsFixing.forEach(item => {
  console.log(`- ${item.file}`);
});
