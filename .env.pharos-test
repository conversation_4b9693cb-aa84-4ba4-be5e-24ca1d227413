# Pharos Test 分支环境配置
# 运行环境
NODE_ENV=development

# 服务器配置
PORT=3456

# 数据库配置 (Pharos Test 专用)
DB_NAME=pharos_test_db
DB_USER=pharos_test
DB_PASS=00321zixunadmin
DB_HOST=mysql
DB_PORT=3306

# Redis 配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASS=joetest1123

# JWT 配置
JWT_SECRET_Master=BugkKneq6aoZmgoT6IfY23K9Kum5YQ
JWT_SECRET_Wallet=S9Ym3BlgGp1k10kWZUiOcTxE2nm5dw

# TON 配置
TONCENTER_API_KEY=42a2067391ac48ffb6c3b96f6eb2a0209f6541caf5cbb368e9dd7ae6820dc4d2
TONCENTER_API_KEY_TEST=af8f3b969da812bef7aea12150729ac1e767f8a77cfa725589204c86bd27b95c

# 邮件配置
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=465
EMAIL_SECURE=true
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=dqcoeatpsoysbjcj
EMAIL_FROM="MooFun Pharos Test" <<EMAIL>>

# 游戏配置
ROOM_CAPACITY=5
BOT_TOKEN="1"

# Telegram 配置
TELEGRAM_BOT_TOKEN="**********************************************"

# 钱包配置
WALLET_A_SEED="young female empty crazy raise outdoor surprise gospel flip observe patch observe nurse illegal erosion priority episode brain rail tongue sing galaxy live estate"
WALLET_B="0QDioPTSTodOvpJe5czY963Jry4QilH3tMBzZn-1vFbhNmLO"
SKIP_PROOF_CHECK=true

# 测试配置
TEST_ETH_PRIVATE_KEY=0x740fe33880d587c4f2a2f1c0190f10ebe0a74afd6073a08da8d78c3c8a7cd39d

# DApp Portal 配置
DAPP_PORTAL_CLIENT_ID=6390fd82-d5da-462f-84e9-4755ad33c04e
DAPP_PORTAL_CLIENT_SECRET=5c3719ec-7d5c-4769-8370-e85c57b21082

# 基础 URL (Pharos Test 环境)
BASE_URL=https://pharos-test.wolf-fun.com

# Kaia 配置
KAIA_PRICE_UPDATE_SCHEDULE=0 */200 * * * *
KAIASCAN_API_KEY=bf3d0731-16e0-4fb9-9a54-c880cf6bc734

# PHRS 配置 (Pharos Test 网络)
PHRS_DEPOSIT_CONTRACT_ADDRESS=******************************************
PHAROS_RPC_URL=https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47
PHRS_HISTORICAL_START_BLOCK=13805521

# 调试配置
DEBUG=true
LOG_LEVEL=debug
